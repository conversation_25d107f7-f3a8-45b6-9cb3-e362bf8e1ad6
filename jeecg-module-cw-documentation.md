# Jeecg-Module-CW 模块功能文档

## 1. 模块概述

`jeecg-module-cw` 是一个复杂的财务（CW）核心业务模块，负责处理和分析与公司运营相关的各种财务数据。该模块被设计为多个子模块的集合，每个子模块处理特定的业务领域，例如模拟利润分析、成本构成、法人单位统计、定时任务等。

**主要子模块包括：**

*   `statistics`: 核心统计分析模块，聚合各业务数据并提供统一的统计视图。
*   `mnlr`: 模拟利润模块，负责模拟利润相关数据的记录与初步计算。
*   `cbgc`: 成本构成模块，管理与成本相关的数据。
*   `frdw`: 法人单位模块，处理与各法人单位相关的数据。
*   `qtfy`: 其他费用模块，管理零散的费用条目。
*   `base`: 提供基础服务和通用枚举/常量。
*   `quartz`: 负责执行定时任务，例如数据日结。
*   (其他模块如 `xjs`, `krb`, `jscb` 等，功能待进一步分析)

本文档旨在详细描述该模块的功能、核心计算逻辑以及模块间的函数调用关系。

---

## 2. `statistics` 子模块

### 2.1. 模块目的

`statistics` 模块是 `jeecg-module-cw` 的数据聚合与展示中心。它不直接产生业务数据，而是作为服务聚合层，从 `mnlr`（模拟利润）、`cbgc`（成本构成）、`frdw`（法人单位）等多个原子业务模块拉取数据，进行汇总、计算和格式化，最终通过 RESTful API 对外提供统一、高度概括的统计数据视图。

### 2.2. API 接口

所有接口均定义在 `CwStatisticsController.java` 中，路径前缀为 `/statistics`。

| Method | URI                     | 功能描述                   | 主要参数                                 |
| :----- | :---------------------- | :------------------------- | :--------------------------------------- |
| `GET`  | `/output/day`           | 查询当日及当月产品产量     | `queryDate` (yyyy-MM-dd)                 |
| `GET`  | `/mnlr/day`             | 查询近期模拟利润（日维度） | `queryDate`, `offset` (向前追溯天数)     |
| `GET`  | `/mnlr/all`             | 查询日、月、年维度的模拟利润 | `queryDate`                              |
| `GET`  | `/frdw`                 | 查询法人单位模拟利润统计   | `queryDate`                              |
| `GET`  | `/cbgc/day`             | 查询当日成本构成           | `queryDate`                              |
| `GET`  | `/price/day`            | 查询当日金属价格           | `queryDate`                              |

### 2.3. 核心逻辑与计算

核心业务逻辑实现在 `CwStatisticsServiceImpl.java` 中。

#### 2.3.1. 模拟利润统计 (`mnlrStatistics`)

这是最核心的计算功能，通过 `getAggregatedStatisticsOptimized` 方法实现，用于计算指定时间范围（日、月、年）的模拟利润。

**计算公式:**
\[ \text{模拟利润} = \text{销售收入} - \text{总成本} - \text{其他费用} + \text{第三方利润} \]

**详细计算步骤:**

1.  **销售收入 (`slSum`)**:
    *   从 `mnlrDayService` 获取日期范围内的所有 `CwMnlrDay` 记录。
    *   遍历每条记录，应用公式：
        \[ \text{单条销售收入} = (\text{价格} \times \text{系数} \times \text{销量}) / 1.13 / 10000 \]
    *   `系数` 来自 `CwMnlrDay.getXs()`，用于单位换算或业务调整。
    *   所有记录的销售收入累加得到 `slSum`。

2.  **总成本 (`cbSum`)**:
    *   直接累加 `CwMnlrDay` 记录中的 `cb` 字段。

3.  **其他费用 (`qtSum`)**:
    *   从 `qtfyService` 获取所有 `CwQtfy` 记录，并累加其 `je` (金额) 字段。

4.  **第三方利润 (`djlrSum`)**:
    *   从 `frdwDayService` 获取所有 `CwFrdwDay` 记录，并累加其 `lrDrs` (利润) 字段。

5.  **公司计划 (`gsjhSum`)**:
    *   从 `kBaseService` 获取年度总计划 (`Year_JH`)。
    *   根据查询的时间范围（日/月/年），按比例折算当期的计划值。

6.  **计划比 (`jhbSum`)**:
    *   计算方式：`模拟利润 - 公司计划`。

#### 2.3.2. 产品产量 (`outputDay`)

*   查询 `queryDate` 前30天的日产量 (`CwMnlrDay.xl` 字段) 作为近期趋势。
*   查询 `queryDate` 所在月份的所有数据，按产品类型 (`type`) 分组，分别计算 **月度累计产量** 和 **月度日均产量**。

#### 2.3.3. 成本构成 (`cbgcDay`)

*   从 `cbgcService` 获取当月至今的所有 `CwCbgc` 数据。
*   进行两次分组累加：
    1.  **按类型**: 累加到 `CwCbgcType` 对象中（如 `cl`-材料, `bj`-备件, `rl`-燃料等）。
    2.  **按单位**: 累加到 `CwCbgcDw` 对象中（如 `frdw`-法人单位, `ckc`-仓库等）。

#### 2.3.4. 法人单位利润 (`frdwStatistics`)

*   通过 `getFrdwDailyStatistics` 获取指定日期范围内的所有法人单位数据 (`CwFrdwDay`)。
*   调用 `aggregateFrdwDay` 方法，按预定义的法人单位类型（如 `xjs`-新技术, `zz`-铸造公司等）进行分组，分别累加每种类型的利润 (`LrDrs`) 和税利 (`SlDrs`)。

### 2.4. 函数调用关系

```mermaid
graph TD
    A[CwStatisticsController] -- 调用 --> B(CwStatisticsServiceImpl);
    B -- 聚合数据 --> C{各业务Service};
    C --> D[ICwMnlrDayService];
    C --> E[ICwCbgcService];
    C --> F[ICwFrdwDayService];
    C --> G[ICwQtfyService];
    C --> H[ICwKBaseService];
```
---

## 3. `mnlr` 子模块 (模拟利润)

### 3.1. 模块目的

`mnlr` 模块是模拟利润计算的基础数据源和第一层计算引擎。它负责管理最原始的业务数据（如各产品的销量、单价），并执行日维度的模拟利润计算，为上层 `statistics` 模块提供日结后的统计数据。

### 3.2. API 接口

主要接口定义在 `CwMnlrDayController.java` 中，路径前缀为 `/mnlr/cwMnlrDay`。

| Method | URI                     | 功能描述                               | 主要参数/请求体                  |
| :----- | :---------------------- | :------------------------------------- | :------------------------------- |
| `POST` | `/submit`               | 提交或更新一天的所有模拟利润原始数据   | `CwMnlrDaySumbitParam`           |
| `GET`  | `/query`                | 查询指定日期的模拟利润录入详情         | `queryDate` (yyyy-MM-dd)         |
| `GET`  | `/calculateStatistics`  | 计算指定日期的所有核心统计指标         | `queryDate`                      |
| `GET`  | `/list`                 | (标准) 分页查询 `CwMnlrDay` 记录       | -                                |
| `POST` | `/add`, `/edit`, `/delete` | (标准) 对 `CwMnlrDay` 的 CRUD 操作     | `CwMnlrDay`                      |

### 3.3. 核心逻辑与计算

核心业务逻辑在 `CwMnlrDayServiceImpl.java` 中。

#### 3.3.1. 数据提交流程 (`submit`)

1.  前端页面（或外部系统）通过 `/submit` 接口，提交包含日期和多个产品行（`CwMnlrDayRow`）的参数 `CwMnlrDaySumbitParam`。
2.  后端首先删除该日期（`record_time`）在 `cw_mnlr_day` 表中的所有旧记录。
3.  然后，将提交的行数据批量保存为新的 `CwMnlrDay` 记录。

#### 3.3.2. 日度统计计算 (`calculateStatistics`)

这是模块最核心的方法，它最终返回一个 `CwMnlrStatisticsResult` 对象，供 `statistics` 模块使用。

**计算步骤:**

1.  **获取日成本 (`getDayCbMap`)**:
    *   该模块不直接存储日成本，而是依赖 `jscb` (金属成本) 模块。
    *   它调用 `jscbService.getJscb(date)` 分别获取**当天**和**前一天**的**累计总成本**。
    *   **日成本 = 当天累计总成本 - 前一天累计总成本**。
    *   如果查询的是某月的第一天，则日成本就等于当天的累计总成本。

2.  **计算销售收入 (`calculateSl`)**:
    *   此处的计算与 `statistics` 模块不同。它直接累加 `CwMnlrDayRow` 中的 `xs` 字段。`xs` 字段的值可能由前端计算好后直接提交，也可能在后端 `queryByDate` 时通过 `价格*销量*系数` 的方式预计算并填充。

3.  **计算总成本 (`calculateCb`)**:
    *   直接累加 `CwMnlrDayRow` 中的 `cb` 字段，这些 `cb` 字段的值由步骤1中的 `getDayCbMap` 计算得出。

4.  **获取其他费用和第三方利润 (`getQt`, `getDjlr`)**:
    *   分别调用 `qtfyService.sumDay()` 和 `frdwService.sumDay()` 获取当天的总额。

5.  **计算日计划 (`calculateGsjh`)**:
    *   从 `kBaseService` 获取**年度计划**。
    *   **逻辑缺陷**: 当前实现中，日计划被错误地计算为 `年度计划 / 12`。这应为 `年度计划 / 365`。此缺陷可能导致计划比 (`jhb`) 计算不准确。

6.  **汇总计算**:
    *   应用与 `statistics` 模块相同的公式计算模拟利润 (`mnlr`) 和计划比 (`jhb`)。
    *   将所有结果（sl, cb, djlr, qt, mnlr, gsjh, jhb）封装到 `CwMnlrStatisticsResult` 对象中返回。

### 3.4. 函数调用关系

```mermaid
graph TD
    A[CwMnlrDayController] --> B(CwMnlrDayServiceImpl);
    B -- "计算日成本" --> JscbService(ICwJscbService);
    B -- "获取其他费用" --> QtfyService(ICwQtfyService);
    B -- "获取法人单位利润" --> FrdwService(ICwFrdwService);
    B -- "获取年度计划" --> KBaseService(ICwKBaseService);
```

---

## 4. `jscb` 子模块 (金属成本)

### 4.1. 模块目的

`jscb` 模块是整个财务系统的**成本计算引擎**。它不直接产生原始业务数据，而是根据用户录入的**月度分摊系数**和**月度基础成本**，结合一个外部传入的**每日累计总成本 (`zcb`)**，通过一系列复杂的加权和分摊公式，计算出各业务类型（如 `t`, `j`, `y` 等）的**每日累计成本**。

该模块的输出是 `mnlr` 模块计算**日成本**的数据来源，是成本计算链路的核心环节。

### 4.2. API 接口

主要接口定义在 `CwJscbController.java` 中，路径前缀为 `/jscb/cwJscb`。

| Method | URI          | 功能描述                                   | 主要参数/请求体        |
| :----- | :----------- | :----------------------------------------- | :--------------------- |
| `POST` | `/submit`    | 提交或更新当月的成本分摊系数和基础成本     | `CwJscbSumbitParam`    |
| `GET`  | `/query`     | 查询指定日期的成本详情（用于页面展示）     | `queryDate`            |
| `GET`  | `/autoFill`  | 获取前一天的成本数据，用于自动填充录入页面 | `queryDate`            |

### 4.3. 核心逻辑与计算

核心逻辑在 `CwJscbServiceImpl.java` 的 `getJscb` 方法中。此方法被 `CwMnlrDayServiceImpl` 调用，以获取各项业务的**累计成本**。

**核心计算流程 (`getJscb`)**:

1.  **获取输入数据**:
    *   **月度基础成本**: 从 `cwKBaseService` 获取4个值：`dyf` (电费), `zycb` (自营成本), `msft` (未知费用), `clcb` (材料成本)。这些值由 `/submit` 接口按月写入。
    *   **月度分摊系数**: 从 `cw_jscb` 表获取当月的分摊系数 `ftxs1`, `ftxs2`, `ftxs3`。这些也由 `/submit` 接口写入。
    *   **每日累计总成本 (`zcb`)**: 调用 `cwKBaseService.getKzcb(queryDate)` 获取。这是最关键的输入，其来源可能是外部系统或另一个未分析的模块。

2.  **计算可分摊成本**:
    *   首先，计算一个待分摊的净成本：
        \[ \text{zcbSub} = \text{zcb} - \text{dyf} - \text{zycb} - \text{msft} - \text{clcb} \]

3.  **执行成本分摊**:
    *   根据一系列硬编码的复杂公式，将 `zcbSub` 和其他基础成本，按照各业务类型（`tjk`, `ljk`, `mjk`等）不同的分攤系数，加权计算出最终的成本。
    *   **示例公式 (tjk成本)**:
        \[ \text{tjkCb} = (\text{zcbSub} \times \text{tjk.ftxs1} / 100) + (\text{zycb} \times \text{tjk.ftxs2} / 100) + \text{dyf} + \text{msft} \]
    *   其他业务（如`t`, `j`, `y`）的成本则基于 `tjkCb` 的计算结果再次进行分摊。

4.  **输出结果**:
    *   方法最终返回一个 `List<CwJsZcb>`，其中包含各业务类型及其对应的**当日累计总成本**。这个结果被 `mnlr` 模块用于计算日增量成本。

### 3.4. 函数调用关系

```mermaid
graph TD
    subgraph mnlr 模块
        A(CwMnlrDayServiceImpl)
    end
    subgraph jscb 模块
        B(CwJscbServiceImpl)
    end
    subgraph base 模块
        C(ICwKBaseService)
    end

    A -- "getJscb(date)" --> B;
    B -- "获取月度基础成本和累计总成本(zcb)" --> C;
```

---

## 5. 总结与整体流程

### 5.1. 功能概述
`jeecg-module-cw` 模块实现了一个日清日结的财务统计系统。其核心功能是根据每日录入的业务数据和成本数据，自动计算模拟利润，并提供多维度的统计查询。

### 5.2. 数据流与计算逻辑
1.  **成本录入 (月度)**: 用户通过 `jscb` 模块的接口，录入当月的**成本分摊系数**和**月度基础成本**。
2.  **成本计算 (每日)**: `jscb` 模块获取一个关键的**外部每日累计总成本 `zcb`**，结合月度系数，分摊计算出各项业务的**每日累计成本**。
3.  **业务录入 (每日)**: 用户通过 `mnlr` 模块的接口，录入各产品的**价格、销量**等原始业务数据。
4.  **日成本计算 (每日)**: `mnlr` 模块调用 `jscb` 模块获取当天和前一天的累计成本，作差得到**日增量成本**。
5.  **日利润计算 (每日)**: `mnlr` 模块结合日增量成本和录入的业务数据，计算出**日模拟利润**等指标，并将结果存入 `cw_mnlr_statistics_day` 表（推测）。
6.  **统计聚合 (查询时)**: `statistics` 模块在用户查询时，从 `mnlr` 等模块获取日结后的数据，进行日、月、年等维度的最终聚合、计算和展示。

### 5.3. 待办与潜在问题
*   **`zcb` 来源**: 关键的每日累计总成本 `zcb` 的来源尚未明确，需进一步分析 `ICwKBaseService.getKzcb()` 的实现。
*   **日计划计算逻辑**: `CwMnlrDayServiceImpl` 中的日计划计算存在逻辑错误（年计划/12），应予以修正。
*   **定时任务**: `quartz` 模块的功能未分析，它很可能负责每日自动触发 `mnlr` 和 `jscb` 的计算，实现日结自动化。 