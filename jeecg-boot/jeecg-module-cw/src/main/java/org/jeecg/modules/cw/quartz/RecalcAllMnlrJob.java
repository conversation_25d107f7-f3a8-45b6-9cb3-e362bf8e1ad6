package org.jeecg.modules.cw.quartz;

import lombok.extern.log4j.Log4j2;
import org.jeecg.modules.cw.mnlr.service.ICwMnlrStatisticsDayService;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 全量重算模拟利润统计 Job
 * <p>
 * 运行时会遍历现有 {@code CwMnlrDay} 数据并重新计算/回写日统计表，
 * 适用于初始化或逻辑调整后的批量修复。
 *
 * <AUTHOR>
 */
@Log4j2
@Service
public class RecalcAllMnlrJob implements Job {

    @Resource
    private ICwMnlrStatisticsDayService statisticsDayService;

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        log.info("[RecalcAllMnlrJob] 开始全量重算模拟利润统计数据");
        try {
            statisticsDayService.recalcAll();
            log.info("[RecalcAllMnlrJob] 全量重算完成");
        } catch (Exception e) {
            log.error("[RecalcAllMnlrJob] 全量重算执行异常", e);
            throw new JobExecutionException(e);
        }
    }
} 