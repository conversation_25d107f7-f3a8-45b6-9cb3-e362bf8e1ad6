package org.jeecg.modules.cw.migration;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.log4j.Log4j2;
import org.jeecg.modules.cw.base.constants.CwCllDataName;
import org.jeecg.modules.cw.base.entity.CwCllCblData;
import org.jeecg.modules.cw.base.service.ICwCllCblDataService;
import org.jeecg.modules.cw.base.service.ICwCllDataService;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * 处理量采拨量数据迁移
 * 将 cw_cll_cbl_data 表中的数据迁移到 cw_cll_data 表中
 * 
 * 默认禁用自动迁移功能，可通过配置 cw.migration.cll-cbl-data.enabled=true 启用
 */
@Log4j2
@Component
@ConditionalOnProperty(prefix = "cw.migration", name = "cll-cbl-data.enabled", havingValue = "true", matchIfMissing = false)
public class CllCblDataMigration implements CommandLineRunner {

    @Resource
    private ICwCllCblDataService cwCllCblDataService;

    @Resource
    private ICwCllDataService cwCllDataService;

    @Override
    public void run(String... args) {
        log.info("开始自动迁移处理量采拨量数据...");
        
        try {
            // 查询所有数据
            List<CwCllCblData> cllCblDataList = cwCllCblDataService.list();
            
            if (ObjectUtil.isEmpty(cllCblDataList)) {
                log.info("没有需要迁移的数据");
                return;
            }
            
            log.info("共找到 {} 条需要迁移的数据", cllCblDataList.size());
            
            // 迁移数据
            int successCount = 0;
            for (CwCllCblData cllCblData : cllCblDataList) {
                try {
                    // 迁移泗州处理量
                    if (ObjectUtil.isNotEmpty(cllCblData.getSzcll())) {
                        cwCllDataService.setCllData(CwCllDataName.SZCLL, cllCblData.getSzcll().toString(), cllCblData.getRecordTime());
                    }
                    
                    // 迁移大山处理量
                    if (ObjectUtil.isNotEmpty(cllCblData.getDscll())) {
                        cwCllDataService.setCllData(CwCllDataName.DSCLL, cllCblData.getDscll().toString(), cllCblData.getRecordTime());
                    }
                    
                    // 迁移采拨总量
                    if (ObjectUtil.isNotEmpty(cllCblData.getCbzl())) {
                        cwCllDataService.setCllData(CwCllDataName.CBZL, cllCblData.getCbzl().toString(), cllCblData.getRecordTime());
                    }
                    
                    successCount++;
                } catch (Exception e) {
                    log.error("迁移数据 {} 时发生错误", cllCblData.getId(), e);
                }
            }
            
            log.info("数据迁移完成，共迁移 {}/{} 条数据", successCount, cllCblDataList.size());
        } catch (Exception e) {
            log.error("数据迁移过程中发生错误", e);
        }
    }
} 