package org.jeecg.modules.cw.sx.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.cw.sx.entity.CwSxMonth;
import org.jeecg.modules.cw.sx.service.ICwSxMonthService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 泗选厂-月填报
 * @Author: jeecg-boot
 * @Date:   2024-12-19
 * @Version: V1.0
 */
@Api(tags="泗选厂-月填报")
@RestController
@RequestMapping("/sx/cwSxMonth")
@Slf4j
public class CwSxMonthController extends JeecgController<CwSxMonth, ICwSxMonthService> {
	@Autowired
	private ICwSxMonthService cwSxMonthService;
	
	/**
	 * 分页列表查询
	 *
	 * @param cwSxMonth
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "泗选厂-月填报-分页列表查询")
	@ApiOperation(value="泗选厂-月填报-分页列表查询", notes="泗选厂-月填报-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<CwSxMonth>> queryPageList(CwSxMonth cwSxMonth,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<CwSxMonth> queryWrapper = QueryGenerator.initQueryWrapper(cwSxMonth, req.getParameterMap());
		Page<CwSxMonth> page = new Page<CwSxMonth>(pageNo, pageSize);
		IPage<CwSxMonth> pageList = cwSxMonthService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param cwSxMonth
	 * @return
	 */
	@AutoLog(value = "泗选厂-月填报-添加")
	@ApiOperation(value="泗选厂-月填报-添加", notes="泗选厂-月填报-添加")
	@RequiresPermissions("sx:cw_sx_month:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody CwSxMonth cwSxMonth) {
		cwSxMonthService.save(cwSxMonth);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param cwSxMonth
	 * @return
	 */
	@AutoLog(value = "泗选厂-月填报-编辑")
	@ApiOperation(value="泗选厂-月填报-编辑", notes="泗选厂-月填报-编辑")
	@RequiresPermissions("sx:cw_sx_month:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody CwSxMonth cwSxMonth) {
		cwSxMonthService.updateById(cwSxMonth);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "泗选厂-月填报-通过id删除")
	@ApiOperation(value="泗选厂-月填报-通过id删除", notes="泗选厂-月填报-通过id删除")
	@RequiresPermissions("sx:cw_sx_month:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		cwSxMonthService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "泗选厂-月填报-批量删除")
	@ApiOperation(value="泗选厂-月填报-批量删除", notes="泗选厂-月填报-批量删除")
	@RequiresPermissions("sx:cw_sx_month:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.cwSxMonthService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "泗选厂-月填报-通过id查询")
	@ApiOperation(value="泗选厂-月填报-通过id查询", notes="泗选厂-月填报-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<CwSxMonth> queryById(@RequestParam(name="id",required=true) String id) {
		CwSxMonth cwSxMonth = cwSxMonthService.getById(id);
		if(cwSxMonth==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(cwSxMonth);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param cwSxMonth
    */
    @RequiresPermissions("sx:cw_sx_month:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, CwSxMonth cwSxMonth) {
        return super.exportXls(request, cwSxMonth, CwSxMonth.class, "泗选厂-月填报");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("sx:cw_sx_month:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, CwSxMonth.class);
    }

}
