package org.jeecg.modules.cw.cbwc.param;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.jeecg.modules.cw.cbwc.entity.CwCbwcRow;
import org.jeecg.modules.cw.xjs.entity.CwXjsRow;

import java.util.Date;
import java.util.List;

@Data
public class CwCbwcSumbitParam {
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date submitDate;
    private List<CwCbwcRow> rows;
}
