package org.jeecg.modules.cw.base.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.config.shiro.IgnoreAuth;
import org.jeecg.modules.cw.base.entity.CwNameDict;
import org.jeecg.modules.cw.base.service.ICwNameDictService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 项目名称字典表
 * @Author: jeecg-boot
 * @Date:   2024-12-12
 * @Version: V1.0
 */
@Api(tags="项目名称字典表")
@RestController
@RequestMapping("/base/cwNameDict")
@Slf4j
public class CwNameDictController extends JeecgController<CwNameDict, ICwNameDictService> {
	@Autowired
	private ICwNameDictService cwNameDictService;
	
	/**
	 * 分页列表查询
	 *
	 * @param cwNameDict
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "项目名称字典表-分页列表查询")
	@ApiOperation(value="项目名称字典表-分页列表查询", notes="项目名称字典表-分页列表查询")
	@GetMapping(value = "/list")
	@IgnoreAuth
	public Result<IPage<CwNameDict>> queryPageList(CwNameDict cwNameDict,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<CwNameDict> queryWrapper = QueryGenerator.initQueryWrapper(cwNameDict, req.getParameterMap());
		Page<CwNameDict> page = new Page<CwNameDict>(pageNo, pageSize);
		IPage<CwNameDict> pageList = cwNameDictService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param cwNameDict
	 * @return
	 */
	@AutoLog(value = "项目名称字典表-添加")
	@ApiOperation(value="项目名称字典表-添加", notes="项目名称字典表-添加")
	@RequiresPermissions("base:cw_name_dict:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody CwNameDict cwNameDict) {
		cwNameDictService.save(cwNameDict);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param cwNameDict
	 * @return
	 */
	@AutoLog(value = "项目名称字典表-编辑")
	@ApiOperation(value="项目名称字典表-编辑", notes="项目名称字典表-编辑")
	@RequiresPermissions("base:cw_name_dict:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody CwNameDict cwNameDict) {
		cwNameDictService.updateById(cwNameDict);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "项目名称字典表-通过id删除")
	@ApiOperation(value="项目名称字典表-通过id删除", notes="项目名称字典表-通过id删除")
	@RequiresPermissions("base:cw_name_dict:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		cwNameDictService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "项目名称字典表-批量删除")
	@ApiOperation(value="项目名称字典表-批量删除", notes="项目名称字典表-批量删除")
	@RequiresPermissions("base:cw_name_dict:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.cwNameDictService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "项目名称字典表-通过id查询")
	@ApiOperation(value="项目名称字典表-通过id查询", notes="项目名称字典表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<CwNameDict> queryById(@RequestParam(name="id",required=true) String id) {
		CwNameDict cwNameDict = cwNameDictService.getById(id);
		if(cwNameDict==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(cwNameDict);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param cwNameDict
    */
    @RequiresPermissions("base:cw_name_dict:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, CwNameDict cwNameDict) {
        return super.exportXls(request, cwNameDict, CwNameDict.class, "项目名称字典表");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("base:cw_name_dict:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, CwNameDict.class);
    }

}
