package org.jeecg.modules.cw.ckc.result;

import lombok.Data;
import org.jeecg.modules.cw.ckc.entity.CwCkcRow;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 采矿场综合成本表查询结果
 */
@Data
public class CwCkcZhcbListResult {
    /**
     * 查询日期
     */
    private Date queryDate;

    /**
     * 行数据
     */
    private List<CwCkcRow> rows;

    /**
     * 采剥总量
     */
    private String cbzl;

    /**
     * 处理量预测
     */
    private String yc;
}
