package org.jeecg.modules.cw.mnlr.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.cw.mnlr.entity.CwMnlrStatisticsDay;
import org.jeecg.modules.cw.mnlr.service.ICwMnlrStatisticsDayService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 矿模拟利润统计数据（日）
 * @Author: jeecg-boot
 * @Date:   2025-02-20
 * @Version: V1.0
 */
@Api(tags="矿模拟利润统计数据（日）")
@RestController
@RequestMapping("/mnlr/cwMnlrStatisticsDay")
@Slf4j
public class CwMnlrStatisticsDayController extends JeecgController<CwMnlrStatisticsDay, ICwMnlrStatisticsDayService> {
	@Autowired
	private ICwMnlrStatisticsDayService cwMnlrStatisticsDayService;
	
	/**
	 * 分页列表查询
	 *
	 * @param cwMnlrStatisticsDay
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "矿模拟利润统计数据（日）-分页列表查询")
	@ApiOperation(value="矿模拟利润统计数据（日）-分页列表查询", notes="矿模拟利润统计数据（日）-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<CwMnlrStatisticsDay>> queryPageList(CwMnlrStatisticsDay cwMnlrStatisticsDay,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<CwMnlrStatisticsDay> queryWrapper = QueryGenerator.initQueryWrapper(cwMnlrStatisticsDay, req.getParameterMap());
		Page<CwMnlrStatisticsDay> page = new Page<CwMnlrStatisticsDay>(pageNo, pageSize);
		IPage<CwMnlrStatisticsDay> pageList = cwMnlrStatisticsDayService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param cwMnlrStatisticsDay
	 * @return
	 */
	@AutoLog(value = "矿模拟利润统计数据（日）-添加")
	@ApiOperation(value="矿模拟利润统计数据（日）-添加", notes="矿模拟利润统计数据（日）-添加")
	@RequiresPermissions("mnlr:cw_mnlr_statistics_day:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody CwMnlrStatisticsDay cwMnlrStatisticsDay) {
		cwMnlrStatisticsDayService.save(cwMnlrStatisticsDay);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param cwMnlrStatisticsDay
	 * @return
	 */
	@AutoLog(value = "矿模拟利润统计数据（日）-编辑")
	@ApiOperation(value="矿模拟利润统计数据（日）-编辑", notes="矿模拟利润统计数据（日）-编辑")
	@RequiresPermissions("mnlr:cw_mnlr_statistics_day:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody CwMnlrStatisticsDay cwMnlrStatisticsDay) {
		cwMnlrStatisticsDayService.updateById(cwMnlrStatisticsDay);
		return Result.OK("编辑成功!");
	}

	 /**
	  *  编辑
	  *
	  * @param mnlrStatisticsDay
	  * @return
	  */
	 @AutoLog(value = "矿模拟利润统计数据（日）-增加或编辑")
	 @ApiOperation(value="矿模拟利润统计数据（日）-增加或编辑", notes="矿模拟利润统计数据（日）-增加或编辑")
	 @RequestMapping(value = "/addOrEdit", method = {RequestMethod.PUT,RequestMethod.POST})
	 public Result<String> addOrEdit(@RequestBody CwMnlrStatisticsDay mnlrStatisticsDay) {
		 cwMnlrStatisticsDayService.addOrEdit(mnlrStatisticsDay);
		 return Result.OK("编辑成功!");
	 }
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "矿模拟利润统计数据（日）-通过id删除")
	@ApiOperation(value="矿模拟利润统计数据（日）-通过id删除", notes="矿模拟利润统计数据（日）-通过id删除")
	@RequiresPermissions("mnlr:cw_mnlr_statistics_day:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		cwMnlrStatisticsDayService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "矿模拟利润统计数据（日）-批量删除")
	@ApiOperation(value="矿模拟利润统计数据（日）-批量删除", notes="矿模拟利润统计数据（日）-批量删除")
	@RequiresPermissions("mnlr:cw_mnlr_statistics_day:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.cwMnlrStatisticsDayService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "矿模拟利润统计数据（日）-通过id查询")
	@ApiOperation(value="矿模拟利润统计数据（日）-通过id查询", notes="矿模拟利润统计数据（日）-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<CwMnlrStatisticsDay> queryById(@RequestParam(name="id",required=true) String id) {
		CwMnlrStatisticsDay cwMnlrStatisticsDay = cwMnlrStatisticsDayService.getById(id);
		if(cwMnlrStatisticsDay==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(cwMnlrStatisticsDay);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param cwMnlrStatisticsDay
    */
    @RequiresPermissions("mnlr:cw_mnlr_statistics_day:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, CwMnlrStatisticsDay cwMnlrStatisticsDay) {
        return super.exportXls(request, cwMnlrStatisticsDay, CwMnlrStatisticsDay.class, "矿模拟利润统计数据（日）");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("mnlr:cw_mnlr_statistics_day:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, CwMnlrStatisticsDay.class);
    }

}
