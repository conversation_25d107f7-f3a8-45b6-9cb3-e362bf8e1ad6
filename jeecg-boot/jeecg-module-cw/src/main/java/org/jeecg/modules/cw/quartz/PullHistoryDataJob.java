package org.jeecg.modules.cw.quartz;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.cw.quartz.service.IPriceDataService;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 历史数据获取Job
 * 用于获取指定时间范围内的历史价格和产量数据
 */
@Slf4j
@Service
public class PullHistoryDataJob implements Job {

    @Autowired
    private IPriceDataService priceDataService;

    @Override
    public void execute(JobExecutionContext ctx) throws JobExecutionException {
        try {
            // 从Job参数中获取开始日期和结束日期
            String parameter = (String) ctx.getJobDetail().getJobDataMap().get("parameter");
            Date startDate = JSONObject.parseObject(parameter).getDate("startDate");
            Date endDate = JSONObject.parseObject(parameter).getDate("endDate");

            if (startDate == null || endDate == null) {
                log.error("历史数据获取Job参数错误：开始日期或结束日期为空");
                return;
            }
            
            // 时间范围校验
            if (startDate.after(endDate)) {
                log.error("历史数据获取Job参数错误：开始日期不能大于结束日期");
                return;
            }
            
            String startDateStr = DateUtil.format(startDate, "yyyy-MM-dd");
            String endDateStr = DateUtil.format(endDate, "yyyy-MM-dd");
            log.info("开始获取{}至{}的历史数据", startDateStr, endDateStr);
            
            // 获取历史价格数据
            priceDataService.getHistoryPriceData(startDate, endDate);
            
            // 获取历史产量数据
            priceDataService.getHistoryOutputData(startDate, endDate);
            
            log.info("历史数据获取完成：{}至{}", startDateStr, endDateStr);
        } catch (Exception e) {
            log.error("历史数据获取异常", e);
            throw new JobExecutionException(e);
        }
    }
} 