export default {
  footer: { onlinePreview: 'JEECG首页', onlineDocument: '在线文档' },
  header: {
    // user dropdown
    dropdownItemDoc: '官网',
    dropdownItemLoginOut: '退出系统',
    dropdownItemSwitchPassword: '密码修改',
    dropdownItemSwitchDepart: '切换部门',
    dropdownItemRefreshCache: '刷新缓存',
    dropdownItemSwitchAccount: '账户设置',

    // tooltip
    tooltipErrorLog: '错误日志',
    tooltipLock: '锁定屏幕',
    tooltipNotify: '消息通知',

    tooltipEntryFull: '全屏',
    tooltipExitFull: '退出全屏',

    // lock
    lockScreenPassword: '锁屏密码',
    lockScreen: '锁定屏幕',
    lockScreenBtn: '锁定',

    home: '首页',
    welcomeIn:"欢迎进入",
    refreshCacheComplete: "刷新缓存完成！",
    refreshCacheFailure: "刷新缓存失败！",
  },
  multipleTab: {
    reload: '刷 新',
    close: '关闭当前',
    closeLeft: '关闭左侧',
    closeRight: '关闭右侧',
    closeOther: '关闭其它',
    closeAll: '关闭全部',
  },
  setting: {
    // content mode
    contentModeFull: '流式',
    contentModeFixed: '定宽',
    // topMenu align
    topMenuAlignLeft: '居左',
    topMenuAlignRight: '居中',
    topMenuAlignCenter: '居右',
    // menu trigger
    menuTriggerNone: '不显示',
    menuTriggerBottom: '底部',
    menuTriggerTop: '顶部',
    // menu type
    menuTypeSidebar: '侧边栏导航',
    menuTypeMixSidebar: '侧边折叠导航',
    menuTypeMix: '顶部混合导航',
    menuTypeTopMenu: '顶部栏导航',

    on: '开',
    off: '关',
    minute: '分钟',

    operatingTitle: '操作成功',
    operatingContent: '复制成功,请到 src/settings/projectSetting.ts 中修改配置！',
    resetSuccess: '重置成功！',

    copyBtn: '拷贝',
    clearBtn: '清空并返回登录',

    drawerTitle: '项目配置',

    darkMode: '主题',
    navMode: '导航栏模式',
    interfaceFunction: '界面设置',
    interfaceDisplay: '界面显示',
    animation: '动画',
    splitMenu: '顶部左侧组合菜单',
    closeMixSidebarOnChange: '切换页面关闭菜单',

    sysTheme: '系统主题',
    headerTheme: '顶栏主题',
    sidebarTheme: '菜单主题',

    menuDrag: '侧边菜单拖拽',
    menuSearch: '菜单搜索',
    menuAccordion: '侧边菜单手风琴模式',
    menuCollapse: '折叠菜单',
    collapseMenuDisplayName: '折叠菜单显示名称',
    topMenuLayout: '顶部菜单布局',
    menuCollapseButton: '菜单折叠按钮',
    contentMode: '内容区域宽度',
    expandedMenuWidth: '菜单展开宽度',

    breadcrumb: '面包屑',
    breadcrumbIcon: '面包屑图标',
    tabs: '标签页',
    tabDetail: '标签详情页',
    tabsQuickBtn: '标签页快捷按钮',
    tabsRedoBtn: '标签页刷新按钮',
    tabsFoldBtn: '标签页折叠按钮',
    tabsTheme: '标签页样式',
    tabsThemeSmooth: '圆滑',
    tabsThemeCard: '卡片',
    tabsThemeSimple: '极简',
    sidebar: '左侧菜单',
    header: '顶栏',
    footer: '页脚',
    fullContent: '全屏内容',
    grayMode: '灰色模式',
    colorWeak: '色弱模式',

    progress: '顶部进度条',
    switchLoading: '切换loading',
    switchAnimation: '切换动画',
    animationType: '动画类型',

    autoScreenLock: '自动锁屏',
    notAutoScreenLock: '不自动锁屏',

    fixedHeader: '固定header',
    fixedSideBar: '固定Sidebar',

    mixSidebarTrigger: '混合菜单触发方式',
    triggerHover: '悬停',
    triggerClick: '点击',

    mixSidebarFixed: '固定展开菜单',
  },
  changePassword: {
    changePassword: '修改密码',
    oldPassword: '旧密码',
    newPassword: '新密码',
    confirmNewPassword: '确认新密码',
    pleaseEnterNewPassword: '请输入新密码',
  },
};
